import { Suspense, lazy } from "react";
import { Route, BrowserRouter as Router, Routes } from "react-router-dom";

import LoadingSpinner from "./components/LoadingSpinner.tsx";
import { AppLayout } from "./components/layouts/AppLayout";
import { Toaster } from "./ui/sonner.tsx";
import ErrorsRouting from "./components/routing/ErrorsRouting.tsx";
import { EnvironmentBanner } from "./components/common/EnvironmentBanner.tsx";

const Onboarding = lazy(() => import("./pages/OnboardingPage.tsx"));
const OnboardingSuccess = lazy(() => import("./pages/OnboardingSuccess.tsx"));
const Home = lazy(() => import("./pages/Home.tsx"));
const PaymentIframe = lazy(() => import("./pages/PaymentIframe.tsx"));
const IframeIntegrationDocs = lazy(() => import("./pages/IframeIntegrationDocs.tsx"));
const IframeDemoPage = lazy(() => import("./pages/IframeDemoPage.tsx"));

function App() {
  return (
    <>
      <EnvironmentBanner />
      <Router>
        <Toaster />
        <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          <Route path="/payment-iframe" element={<PaymentIframe />} />

          <Route element={<AppLayout />}>
            <Route path="/" element={<Home />} />

            <Route path="/onboarding" element={<Onboarding />} />
            <Route path="/onboarding-success" element={<OnboardingSuccess />} />

            <Route path="/iframe-integration" element={<IframeIntegrationDocs />} />
            <Route path="/iframe-demo" element={<IframeDemoPage />} />

            <Route path="/error/*" element={<ErrorsRouting />} />
            <Route path="*" element={<ErrorsRouting />} />
          </Route>
        </Routes>
      </Suspense>
    </Router>
    </>
  );
}

export default App;
