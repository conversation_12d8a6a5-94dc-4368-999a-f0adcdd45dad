import { useState } from "react";
import { toast } from "sonner";
import { formatSuccessMessage, formatErrorMessage } from "../utils/paymentUtils";
import type { PaymentInfo } from "../types/payment";

// Payment Messages
const MESSAGES = {
  SUCCESS: "Payment processed successfully!",
  DEFAULT_ERROR: "Payment processing failed",
} as const;

// Event Types
const PAYMENT_EVENTS = {
  SUCCESS: "PAYMENT_SUCCESS",
  FAILURE: "PAYMENT_FAILURE",
} as const;

// Cancellation Status Codes
const CANCELLATION_CODES = {
  CANCELED: "CANCELED",
  CANCELLED: "CANCELLED",
} as const;

// Communication Target
const WILDCARD_TARGET = "*" as const;

interface UsePaymentHandlersProps {
  paymentInfo?: PaymentInfo | null;
}

interface UsePaymentHandlersReturn {
  success: boolean;
  error: string | null;
  setError: React.Dispatch<React.SetStateAction<string | null>>;
  handlePaymentSuccess: (response: unknown) => void;
  handlePaymentFailure: (error: unknown) => void;
}

export const usePaymentHandlers = ({ paymentInfo }: UsePaymentHandlersProps = {}): UsePaymentHandlersReturn => {
  const [success, setSuccess] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handlePaymentSuccess = (response: unknown) => {
    setSuccess(true);

    if (window.parent !== window) {
      window.parent.postMessage(formatSuccessMessage(response, PAYMENT_EVENTS.SUCCESS), WILDCARD_TARGET);
    }

    // Handle redirect if specified
    if (paymentInfo?.returnUrl) {
      const returnUrl = paymentInfo.returnUrl;
      setTimeout(() => {
        if (window.parent !== window) {
          window.parent.postMessage(
            formatSuccessMessage(returnUrl, "PAYMENT_REDIRECT", {
              url: returnUrl,
            }),
            WILDCARD_TARGET
          );
        } else {
          window.location.href = returnUrl;
        }
      }, 2000);
    }
  };

  const handlePaymentFailure = (error: unknown) => {
    const err = error as { statusCode?: string; message?: string };
    if (err?.statusCode === CANCELLATION_CODES.CANCELED || err?.statusCode === CANCELLATION_CODES.CANCELLED) {
      return;
    }

    const errorMessage = error instanceof Error ? error.message : (error as { message?: string })?.message || MESSAGES.DEFAULT_ERROR;

    setError(errorMessage);
    toast.error(errorMessage);

    if (window.parent !== window) {
      window.parent.postMessage(formatErrorMessage(error, PAYMENT_EVENTS.FAILURE), WILDCARD_TARGET);
    }
  };

  return {
    success,
    error,
    setError,
    handlePaymentSuccess,
    handlePaymentFailure,
  };
};
