import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { type RootState } from "../../redux/store";
import { updateFormData, nextStep, prevStep } from "../../redux/slices/onboardingSlice";
import { FormContainer, FormHeader, FormActions } from "./components";
import { PolicyFieldRenderer } from "../common/PolicyFieldRenderer";
import { validateCompliancePolicies } from "../../utils/validation";
import type { CompliancePolicies, URLOnlyPolicy } from "../../types/payment";

const PolicyCollectionForm = () => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const defaultPolicy = (type: URLOnlyPolicy["type"], title: string): URLOnlyPolicy => ({
    type,
    title,
    content: "", // Optional for URL-only policy
    url: "", // Required but starts empty
    version: "1.0",
    lastUpdated: new Date().toISOString().split("T")[0],
  });

  const [policies, setPolicies] = useState<CompliancePolicies>(
    formData.compliancePolicies || {
      returnRefundPolicy: defaultPolicy("return_refund", "Return and Refund Policy"),
      privacyPolicy: defaultPolicy("privacy", "Consumer Data Privacy Policy"),
      securityPolicy: defaultPolicy("security", "Secure Checkout Policy"),
      termsAndConditions: defaultPolicy("terms", "Terms and Conditions"),
      deliveryPolicy: defaultPolicy("delivery", "Delivery Policy"),
    }
  );

  const validatePolicies = (): boolean => {
    const newErrors = validateCompliancePolicies(policies);
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handlePolicyChange = (policyKey: keyof CompliancePolicies, field: keyof URLOnlyPolicy, value: string) => {
    setPolicies((prev) => {
      const currentPolicy = prev[policyKey];
      if (!currentPolicy) {
        return prev;
      }

      return {
        ...prev,
        [policyKey]: {
          ...currentPolicy,
          [field]: value,
        },
      };
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validatePolicies()) {
      // Prepare compliance policies for submission
      // Only include deliveryPolicy if it has both title and URL
      const compliancePoliciesForSubmission: CompliancePolicies = {
        returnRefundPolicy: policies.returnRefundPolicy,
        privacyPolicy: policies.privacyPolicy,
        securityPolicy: policies.securityPolicy,
        termsAndConditions: policies.termsAndConditions,
      };

      // Only include deliveryPolicy if it's properly filled out
      if (policies.deliveryPolicy?.title && policies.deliveryPolicy?.url) {
        compliancePoliciesForSubmission.deliveryPolicy = policies.deliveryPolicy;
      }

      dispatch(updateFormData({ compliancePolicies: compliancePoliciesForSubmission }));
      dispatch(nextStep());
    }
  };

  const handleBack = () => {
    dispatch(prevStep());
  };


  return (
    <FormContainer>
      <FormHeader title="E-Commerce Compliance Policies" subtitle="Configure required and optional policies for e-commerce disclosure requirements" />

      <form onSubmit={handleSubmit} className="px-8 py-8">
        <PolicyFieldRenderer
          policyKey="returnRefundPolicy"
          policy={policies.returnRefundPolicy}
          label="Return & Refund Policy"
          required={true}
          description="Must specify your refund policy, even if no refunds are accepted."
          errors={errors}
          onPolicyChange={handlePolicyChange}
          variant="onboarding"
        />
        <PolicyFieldRenderer
          policyKey="privacyPolicy"
          policy={policies.privacyPolicy}
          label="Consumer Data Privacy Policy"
          required={true}
          description="Must disclose how you gather, use, and manage customer data."
          errors={errors}
          onPolicyChange={handlePolicyChange}
          variant="onboarding"
        />
        <PolicyFieldRenderer
          policyKey="securityPolicy"
          policy={policies.securityPolicy}
          label="Secure Checkout Policy"
          required={true}
          description="Must list security capabilities for payment cardholder data transmission."
          errors={errors}
          onPolicyChange={handlePolicyChange}
          variant="onboarding"
        />
        <PolicyFieldRenderer
          policyKey="termsAndConditions"
          policy={policies.termsAndConditions}
          label="Terms & Conditions"
          required={true}
          description="Must list all legal terms consumers agree to when purchasing."
          errors={errors}
          onPolicyChange={handlePolicyChange}
          variant="onboarding"
        />
        <PolicyFieldRenderer
          policyKey="deliveryPolicy"
          policy={policies.deliveryPolicy}
          label="Delivery Policy"
          required={false}
          description="Optional: Only required if you sell physical goods that require delivery."
          errors={errors}
          onPolicyChange={handlePolicyChange}
          variant="onboarding"
        />

        <FormActions submitText="Continue to Review" onCancel={handleBack} showCancel={true} cancelText="Back" />
      </form>
    </FormContainer>
  );
};

export default PolicyCollectionForm;
