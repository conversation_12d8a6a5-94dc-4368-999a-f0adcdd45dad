import { useState, useEffect, useCallback } from "react";
import { validateIpAddress } from "../../../utils/ipValidation";
import { IP_DETECTION_CONFIG } from "../../../constants/external-apis";

const IP_TIMEOUT_MS = 5000;

interface IpDetectionResult {
  ip: string;
  isLoading: boolean;
  error: string | null;
  source: string | null;
  isValid: boolean;
}

interface IpService {
  name: string;
  url: string;
  extractIp: (data: Record<string, unknown>) => string;
}

export const useClientIp = (): IpDetectionResult => {
  const [result, setResult] = useState<IpDetectionResult>({
    ip: IP_DETECTION_CONFIG.DEFAULT_IP,
    isLoading: true,
    error: null,
    source: null,
    isValid: false,
  });

  const detectIpFromService = useCallback(async (service: IpService): Promise<string | null> => {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), IP_TIMEOUT_MS);

      const response = await fetch(service.url, {
        signal: controller.signal,
        headers: {
          Accept: "application/json",
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const ip = service.extractIp(data);

      if (!ip) {
        throw new Error("No IP address found in response");
      }

      const ipValidation = validateIpAddress(ip);
      if (!ipValidation.isValid) {
        throw new Error(`Invalid IP address format: ${ip} - ${ipValidation.error}`);
      }

      return ip;
    } catch {
      return null;
    }
  }, []);

  const detectClientIp = useCallback(async () => {
    setResult((prev) => ({ ...prev, isLoading: true, error: null }));

    for (const service of IP_DETECTION_CONFIG.SERVICES) {
      const ip = await detectIpFromService(service);

      if (ip) {
        setResult({
          ip,
          isLoading: false,
          error: null,
          source: service.name,
          isValid: true,
        });
        return;
      }
    }

    // All services failed
    setResult({
      ip: IP_DETECTION_CONFIG.DEFAULT_IP,
      isLoading: false,
      error: "Failed to detect IP address from all services. Using fallback.",
      source: "fallback",
      isValid: false,
    });
  }, [detectIpFromService]);

  useEffect(() => {
    detectClientIp();
  }, [detectClientIp]);

  return result;
};
