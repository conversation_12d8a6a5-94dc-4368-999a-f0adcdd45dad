import type { CompliancePolicies } from "../../../types/payment";

interface ReviewPolicySectionProps {
  formData: {
    compliancePolicies?: CompliancePolicies;
  };
  onEdit: () => void;
}

export const ReviewPolicySection = ({ formData, onEdit }: ReviewPolicySectionProps) => {
  const { compliancePolicies } = formData;

  if (!compliancePolicies) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Compliance Policies</h3>
          <button onClick={onEdit} className="text-blue-600 hover:text-blue-700 font-medium text-sm">
            Add Policies
          </button>
        </div>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-yellow-800 text-sm">Compliance policies are required for payment processing. Please add your policies.</p>
        </div>
      </div>
    );
  }

  const policies = [
    { key: "returnRefundPolicy", label: "Return & Refund Policy", required: true, requirement: "Required" },
    { key: "privacyPolicy", label: "Consumer Data Privacy Policy", required: true, requirement: "Required" },
    { key: "securityPolicy", label: "Secure Checkout Policy", required: true, requirement: "Required" },
    { key: "termsAndConditions", label: "Terms & Conditions", required: true, requirement: "Required" },
    { key: "deliveryPolicy", label: "Delivery Policy", required: false, requirement: "Optional" },
  ];

  const renderPolicyItem = (policyKey: string, label: string, required: boolean, requirement: string) => {
    const policy = compliancePolicies[policyKey as keyof CompliancePolicies];

    return (
      <div key={policyKey} className="border-b border-gray-100 last:border-b-0 py-3">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <h4 className="font-medium text-gray-900 flex items-center">
              {label}
              {required && <span className="text-red-500 ml-1">*</span>}
              <span className={`ml-2 text-xs px-2 py-1 rounded ${required ? "bg-orange-50 text-orange-700" : "bg-gray-50 text-gray-600"}`}>
                {requirement}
              </span>
              {policy ? (
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                  ✓ Configured
                </span>
              ) : (
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">Missing</span>
              )}
            </h4>
            {policy && (
              <div className="mt-2 text-sm text-gray-600">
                <p>
                  <strong>Title:</strong> {policy.title}
                </p>
                <p>
                  <strong>URL:</strong>{" "}
                  <a href={policy.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                    {policy.url}
                  </a>
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const missingPolicies = policies.filter((p) => p.required && !compliancePolicies[p.key as keyof CompliancePolicies]);
  const hasAllRequired = missingPolicies.length === 0;

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Compliance Policies</h3>
        <button onClick={onEdit} className="text-blue-600 hover:text-blue-700 font-medium text-sm">
          Edit Policies
        </button>
      </div>

      {!hasAllRequired && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
          <p className="text-red-800 text-sm">⚠️ Missing required policies: {missingPolicies.map((p) => p.label).join(", ")}</p>
        </div>
      )}

      <div className="space-y-0">{policies.map((policy) => renderPolicyItem(policy.key, policy.label, policy.required, policy.requirement))}</div>

      {hasAllRequired && (
        <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
          <p className="text-green-800 text-sm">All required compliance policies are configured and ready for payment processing.</p>
        </div>
      )}
    </div>
  );
};
