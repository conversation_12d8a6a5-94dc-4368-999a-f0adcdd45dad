import { CompliancePolicies, URLOnlyPolicy, MerchantPolicy } from "../../types/payment";

interface PolicyFieldRendererProps {
  policyKey: keyof CompliancePolicies;
  policy: URLOnlyPolicy | MerchantPolicy | undefined;
  label: string;
  required?: boolean;
  description?: string;
  errors?: Record<string, string>;
  onPolicyChange: (policyKey: keyof CompliancePolicies, field: keyof URLOnlyPolicy, value: string) => void;
  variant?: "onboarding" | "demo";
}

export const PolicyFieldRenderer = ({
  policyKey,
  policy,
  label,
  required = true,
  description,
  errors,
  onPolicyChange,
  variant = "onboarding",
}: PolicyFieldRendererProps) => {
  if (!policy) return null;

  const isOnboarding = variant === "onboarding";
  const hasContent = "content" in policy;

  const containerClasses = isOnboarding
    ? "mb-6 p-4 border border-gray-200 rounded-lg"
    : "space-y-3 p-4 border border-slate-200 rounded-lg bg-slate-50";

  const labelClasses = isOnboarding ? "text-md font-medium text-gray-900 mb-3" : "font-medium text-slate-900";

  const inputClasses = isOnboarding
    ? "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
    : "w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 text-sm";

  return (
    <div className={containerClasses}>
      <h4 className={labelClasses}>
        {label} {required && <span className="text-red-500">*</span>}
        {!required && isOnboarding && <span className="text-gray-500">(Optional)</span>}
        {isOnboarding && <span className="ml-2 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">URL-only policy</span>}
      </h4>

      {description && isOnboarding && <p className="text-sm text-gray-600 mb-3">{description}</p>}

      <div className={isOnboarding ? "space-y-3" : "space-y-3"}>
        <div>
          <label className={isOnboarding ? "block text-sm font-medium text-gray-700 mb-1" : "block text-sm font-medium text-slate-700 mb-1"}>
            Policy Title {required && <span className="text-red-500">*</span>}
          </label>
          <input
            type="text"
            value={policy.title}
            onChange={(e) => onPolicyChange(policyKey, "title", e.target.value)}
            className={inputClasses}
            placeholder={isOnboarding ? `Enter ${label.toLowerCase()} title` : "Policy title"}
          />
          {errors?.[`${policyKey}.title`] && <p className="text-red-500 text-xs mt-1">{errors[`${policyKey}.title`]}</p>}
        </div>

        {hasContent && !isOnboarding && (
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">Content</label>
            <textarea
              value={policy.content || ""}
              onChange={(e) => onPolicyChange(policyKey, "content", e.target.value)}
              rows={3}
              className={inputClasses}
              placeholder="Policy content"
            />
          </div>
        )}

        <div>
          <label className={isOnboarding ? "block text-sm font-medium text-gray-700 mb-1" : "block text-sm font-medium text-slate-700 mb-1"}>
            Policy URL {required && isOnboarding ? <span className="text-red-500">*</span> : "(Optional)"}
          </label>
          <input
            type="url"
            value={policy.url || ""}
            onChange={(e) => onPolicyChange(policyKey, "url", e.target.value)}
            className={inputClasses}
            placeholder={
              isOnboarding ? `https://yourwebsite.com/${label.toLowerCase().replace(/\s+/g, "-")}` : "https://example.com/policy"
            }
          />
          {errors?.[`${policyKey}.url`] && <p className="text-red-500 text-xs mt-1">{errors[`${policyKey}.url`]}</p>}
        </div>
      </div>
    </div>
  );
};