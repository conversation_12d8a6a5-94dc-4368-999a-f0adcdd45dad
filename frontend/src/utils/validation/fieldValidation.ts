import { REGEX_PATTERNS, VALIDATION_MESSAGES } from "../../constants/validation";

export const validateEmail = (email: string): string | null => {
  if (!email) return VALIDATION_MESSAGES.REQUIRED("Email");
  if (!REGEX_PATTERNS.EMAIL.test(email)) return VALIDATION_MESSAGES.INVALID_EMAIL;
  return null;
};

export const validatePhone = (phone: string): string | null => {
  const cleanPhone = phone.replace(REGEX_PATTERNS.DIGITS_ONLY, "");
  if (!cleanPhone) return VALIDATION_MESSAGES.REQUIRED("Phone number");
  if (!REGEX_PATTERNS.PHONE.test(cleanPhone)) return VALIDATION_MESSAGES.INVALID_PHONE;
  return null;
};

export const validateSSN = (ssn: string): string | null => {
  const cleanSSN = ssn.replace(REGEX_PATTERNS.DIGITS_ONLY, "");
  if (!cleanSSN) return VALIDATION_MESSAGES.REQUIRED("SSN");
  if (!REGEX_PATTERNS.SSN.test(cleanSSN)) return VALIDATION_MESSAGES.INVALID_SSN;
  return null;
};

export const validateEIN = (ein: string): string | null => {
  const cleanEIN = ein.replace(REGEX_PATTERNS.DIGITS_ONLY, "");
  if (!cleanEIN) return VALIDATION_MESSAGES.REQUIRED("EIN");
  if (!REGEX_PATTERNS.EIN.test(cleanEIN)) return VALIDATION_MESSAGES.INVALID_EIN;
  return null;
};

export const validateZipCode = (zip: string): string | null => {
  if (!zip) return VALIDATION_MESSAGES.REQUIRED("Zip code");
  if (!REGEX_PATTERNS.ZIP.test(zip)) return VALIDATION_MESSAGES.INVALID_ZIP;
  return null;
};

export const validateURL = (url: string, required: boolean = false): string | null => {
  if (!url && required) return VALIDATION_MESSAGES.REQUIRED("URL");
  if (url && !REGEX_PATTERNS.URL.test(url)) return VALIDATION_MESSAGES.INVALID_URL;
  return null;
};

export const validateRequired = (value: string | undefined, fieldName: string): string | null => {
  if (!value || value.trim().length === 0) {
    return VALIDATION_MESSAGES.REQUIRED(fieldName);
  }
  return null;
};

export const validateMinLength = (value: string, minLength: number, fieldName: string): string | null => {
  if (value.length < minLength) {
    return VALIDATION_MESSAGES.MIN_LENGTH(fieldName, minLength);
  }
  return null;
};

export const validateMaxLength = (value: string, maxLength: number, fieldName: string): string | null => {
  if (value.length > maxLength) {
    return VALIDATION_MESSAGES.MAX_LENGTH(fieldName, maxLength);
  }
  return null;
};

const MIN_AGE = 18;
const MAX_AGE = 120;

export const validateDateOfBirth = (dob: string): string | null => {
  if (!dob) return VALIDATION_MESSAGES.REQUIRED("Date of birth");
  
  const birthDate = new Date(dob);
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  if (age < MIN_AGE) return VALIDATION_MESSAGES.MUST_BE_18;
  if (age > MAX_AGE) return VALIDATION_MESSAGES.INVALID_DATE;
  
  return null;
};

const OWNERSHIP_MIN = 0;
const OWNERSHIP_MAX = 100;
const PASSWORD_MIN_LENGTH = 8;
const PASSWORD_UPPERCASE_REGEX = /[A-Z]/;
const PASSWORD_LOWERCASE_REGEX = /[a-z]/;
const PASSWORD_NUMBER_REGEX = /[0-9]/;
const PASSWORD_SPECIAL_REGEX = /[!@#$%^&*]/;

export const validateOwnershipPercentage = (percentage: number): string | null => {
  if (percentage < OWNERSHIP_MIN) return "Ownership cannot be negative";
  if (percentage > OWNERSHIP_MAX) return "Ownership cannot exceed 100%";
  return null;
};

export const validatePasswordStrength = (password: string): string | null => {
  if (!password) return VALIDATION_MESSAGES.REQUIRED("Password");
  if (password.length < PASSWORD_MIN_LENGTH) return VALIDATION_MESSAGES.MIN_LENGTH("Password", PASSWORD_MIN_LENGTH);
  if (!PASSWORD_UPPERCASE_REGEX.test(password)) return "Password must contain at least one uppercase letter";
  if (!PASSWORD_LOWERCASE_REGEX.test(password)) return "Password must contain at least one lowercase letter";
  if (!PASSWORD_NUMBER_REGEX.test(password)) return "Password must contain at least one number";
  if (!PASSWORD_SPECIAL_REGEX.test(password)) return "Password must contain at least one special character";
  return null;
};

export const validatePasswordMatch = (password: string, confirmPassword: string): string | null => {
  if (password !== confirmPassword) return "Passwords do not match";
  return null;
};