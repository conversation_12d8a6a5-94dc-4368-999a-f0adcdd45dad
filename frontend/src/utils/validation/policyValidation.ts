import { CompliancePolicies, URLOnlyPolicy } from "../../types/payment";

interface PolicyValidationError {
  policyKey: string;
  field: string;
  message: string;
}

export const validatePolicy = (
  policy: URLOnlyPolicy | undefined,
  policyKey: string,
  policyName: string,
  required: boolean
): PolicyValidationError[] => {
  const errors: PolicyValidationError[] = [];

  if (required && !policy) {
    errors.push({
      policyKey,
      field: "missing",
      message: `${policyName} is missing`,
    });
    return errors;
  }

  if (!policy) return errors;

  if (required || policy.title || policy.url) {
    if (!policy.type) {
      errors.push({
        policyKey,
        field: "type",
        message: `${policyName} type is missing`,
      });
    }

    if (!policy.title || policy.title.length < 1) {
      errors.push({
        policyKey,
        field: "title",
        message: `${policyName} title is required`,
      });
    }

    if (!policy.url || policy.url.length < 1) {
      errors.push({
        policyKey,
        field: "url",
        message: `${policyName} URL is required`,
      });
    }
  }

  return errors;
};

export const validateCompliancePolicies = (policies: CompliancePolicies): Record<string, string> => {
  const errors: Record<string, string> = {};
  
  const requiredPolicies = [
    { key: "returnRefundPolicy", name: "Return & Refund Policy" },
    { key: "privacyPolicy", name: "Consumer Data Privacy Policy" },
    { key: "securityPolicy", name: "Secure Checkout Policy" },
    { key: "termsAndConditions", name: "Terms and Conditions" },
  ];

  for (const { key, name } of requiredPolicies) {
    const policyErrors = validatePolicy(
      policies[key as keyof CompliancePolicies],
      key,
      name,
      true
    );
    
    for (const error of policyErrors) {
      errors[`${error.policyKey}.${error.field}`] = error.message;
    }
  }

  const deliveryPolicyErrors = validatePolicy(
    policies.deliveryPolicy,
    "deliveryPolicy",
    "Delivery Policy",
    false
  );
  
  for (const error of deliveryPolicyErrors) {
    errors[`${error.policyKey}.${error.field}`] = error.message;
  }

  return errors;
};

export const isPolicyComplete = (policy: URLOnlyPolicy | undefined, required: boolean): boolean => {
  if (!policy) return !required;
  
  if (required) {
    return !!(policy.title && policy.url && policy.type);
  }
  
  if (policy.title || policy.url) {
    return !!(policy.title && policy.url && policy.type);
  }
  
  return true;
};

export const areAllPoliciesComplete = (policies: CompliancePolicies): boolean => {
  const requiredComplete = 
    isPolicyComplete(policies.returnRefundPolicy, true) &&
    isPolicyComplete(policies.privacyPolicy, true) &&
    isPolicyComplete(policies.securityPolicy, true) &&
    isPolicyComplete(policies.termsAndConditions, true);
  
  const optionalComplete = isPolicyComplete(policies.deliveryPolicy, false);
  
  return requiredComplete && optionalComplete;
};