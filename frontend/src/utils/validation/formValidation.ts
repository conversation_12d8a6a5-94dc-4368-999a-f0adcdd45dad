interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export const createValidationError = (field: string, message: string): ValidationError => ({
  field,
  message,
});

export const combineValidationResults = (...results: ValidationResult[]): ValidationResult => {
  const allErrors = results.flatMap((result) => result.errors);
  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
  };
};

export const validateFormField = (
  value: unknown,
  validators: Array<(value: unknown) => string | null>
): string | null => {
  for (const validator of validators) {
    const error = validator(value);
    if (error) return error;
  }
  return null;
};

export const createFieldValidator = <T>(
  fieldName: string,
  validators: Array<(value: T) => string | null>
) => {
  return (value: T): ValidationError | null => {
    for (const validator of validators) {
      const error = validator(value);
      if (error) {
        return createValidationError(fieldName, error);
      }
    }
    return null;
  };
};

export const validateForm = <T extends Record<string, unknown>>(
  data: T,
  validators: Partial<Record<keyof T, Array<(value: unknown) => string | null>>>
): ValidationResult => {
  const errors: ValidationError[] = [];

  for (const [field, fieldValidators] of Object.entries(validators)) {
    if (!fieldValidators) continue;
    
    const value = data[field as keyof T];
    for (const validator of fieldValidators) {
      const error = validator(value);
      if (error) {
        errors.push(createValidationError(field, error));
        break;
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const getErrorsAsRecord = (errors: ValidationError[]): Record<string, string> => {
  return errors.reduce((acc, error) => {
    acc[error.field] = error.message;
    return acc;
  }, {} as Record<string, string>);
};

export const hasFieldError = (errors: ValidationError[], field: string): boolean => {
  return errors.some((error) => error.field === field);
};

export const getFieldError = (errors: ValidationError[], field: string): string | undefined => {
  const error = errors.find((error) => error.field === field);
  return error?.message;
};