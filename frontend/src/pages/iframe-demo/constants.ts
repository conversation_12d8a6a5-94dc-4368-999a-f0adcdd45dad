import { DEMO_CONFIG, LIMIT<PERSON> } from "../../constants/defaults";

export const DEFAULT_DEMO_CONFIG = {
  merchantId: DEMO_CONFIG.MERCHANT_ID,
  description: DEMO_CONFIG.DESCRIPTION,
  amount: DEMO_CONFIG.AMOUNT_CENTS,
  returnUrl: DEMO_CONFIG.RETURN_URL,
  enableGooglePay: DEMO_CONFIG.GOOGLE_PAY_ENABLED,
  googlePayEnvironment: DEMO_CONFIG.GOOGLE_PAY_ENVIRONMENT,
  googlePayMerchantName: DEMO_CONFIG.GOOGLE_PAY_MERCHANT_NAME,
};

export const DEMO_SECTIONS = [
  { id: "configuration", title: "Payment Configuration" },
  { id: "iframe-demo", title: "Payment Iframe" },
  { id: "event-log", title: "Event Log" },
  { id: "integration-url", title: "Integration URL" },
];

export const EVENT_TYPES = {
  IFRAME_READY: "PAYMENT_IFRAME_READY",
  SUCCESS: "PAYMENT_SUCCESS",
  FAILURE: "PAYMENT_FAILURE",
  VALIDATION_FAILURE: "PAYMENT_VALIDATION_FAILURE",
  TIMEOUT: "PAYMENT_TIMEOUT",
} as const;

export const MAX_EVENTS = LIMITS.MAX_EVENTS;
