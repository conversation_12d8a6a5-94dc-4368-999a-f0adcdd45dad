import { CompliancePolicies } from "../../../types/payment";
import { PolicyFieldRenderer } from "../../../components/common/PolicyFieldRenderer";
import { TEST_COMPLIANCE_POLICIES } from "../../../constants/test-data";

interface CompliancePoliciesFormProps {
  policies: CompliancePolicies | undefined;
  setPolicies: (policies: CompliancePolicies | undefined) => void;
}

const defaultPolicies: CompliancePolicies = TEST_COMPLIANCE_POLICIES;

export const CompliancePoliciesForm = ({ policies, setPolicies }: CompliancePoliciesFormProps) => {
  const handleTogglePolicies = (enabled: boolean) => {
    if (enabled) {
      setPolicies(defaultPolicies);
    } else {
      setPolicies(undefined);
    }
  };

  const handlePolicyChange = (policyKey: keyof CompliancePolicies, field: string, value: string) => {
    if (!policies) return;

    setPolicies({
      ...policies,
      [policyKey]: {
        ...policies[policyKey],
        [field]: value,
      },
    });
  };


  return (
    <div className="border-t border-slate-200 pt-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-slate-900">Compliance Policies</h3>
        <div className="flex items-center">
          <input
            type="checkbox"
            id="enablePolicies"
            checked={!!policies}
            onChange={(e) => handleTogglePolicies(e.target.checked)}
            className="h-4 w-4 text-slate-600 focus:ring-slate-500 border-gray-300 rounded"
          />
          <label htmlFor="enablePolicies" className="ml-2 block text-sm text-slate-700">
            Enable E-commerce Compliance
          </label>
        </div>
      </div>

      {policies && (
        <div className="space-y-4">
          <p className="text-sm text-slate-600 mb-4">
            Configure the required compliance policies for e-commerce integration. These policies will be displayed during checkout.
          </p>

          <PolicyFieldRenderer
            policyKey="returnRefundPolicy"
            policy={policies.returnRefundPolicy}
            label="Return & Refund Policy"
            required={true}
            onPolicyChange={handlePolicyChange}
            variant="demo"
          />
          <PolicyFieldRenderer
            policyKey="deliveryPolicy"
            policy={policies.deliveryPolicy}
            label="Delivery Policy"
            required={false}
            onPolicyChange={handlePolicyChange}
            variant="demo"
          />
          <PolicyFieldRenderer
            policyKey="privacyPolicy"
            policy={policies.privacyPolicy}
            label="Privacy Policy"
            required={true}
            onPolicyChange={handlePolicyChange}
            variant="demo"
          />
          <PolicyFieldRenderer
            policyKey="securityPolicy"
            policy={policies.securityPolicy}
            label="Security Policy"
            required={true}
            onPolicyChange={handlePolicyChange}
            variant="demo"
          />
          <PolicyFieldRenderer
            policyKey="termsAndConditions"
            policy={policies.termsAndConditions}
            label="Terms & Conditions"
            required={true}
            onPolicyChange={handlePolicyChange}
            variant="demo"
          />
        </div>
      )}
    </div>
  );
};
