import { apiRequest } from "../utils/apiRequest";
import type {
  CreatePayrixMerchantRequest,
  MerchantResponse,
  CreateNoteRequest,
  CreateNoteResponse,
  CreateNoteDocumentRequest,
  CreateNoteDocumentResponse,
  CreatePlaidLinkTokenRequest,
  CreatePlaidLinkTokenResponse,
  ProcessPlaidAccountRequest,
  ProcessPlaidAccountResponse,
} from "../types/merchant";

export const createMerchant = async (merchantData: CreatePayrixMerchantRequest): Promise<MerchantResponse> => {
  return apiRequest<MerchantResponse>({
    method: "POST",
    url: "/merchants/onboard",
    data: merchantData,
  });
};

export const createNote = async (noteData: CreateNoteRequest): Promise<CreateNoteResponse> => {
  return apiRequest<CreateNoteResponse>({
    method: "POST",
    url: "/merchants/notes",
    data: noteData,
  });
};

export const createNoteDocument = async (documentData: CreateNoteDocumentRequest): Promise<CreateNoteDocumentResponse> => {
  const formData = new FormData();
  formData.append("noteId", documentData.noteId);
  formData.append("file", documentData.file);
  if (documentData.description) {
    formData.append("description", documentData.description);
  }

  return apiRequest<CreateNoteDocumentResponse>({
    method: "POST",
    url: "/merchants/note-documents",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

export const createPlaidLinkToken = async (tokenData: CreatePlaidLinkTokenRequest): Promise<CreatePlaidLinkTokenResponse> => {
  return apiRequest<CreatePlaidLinkTokenResponse>({
    method: "POST",
    url: "/merchants/plaid/link-token",
    data: tokenData,
  });
};

export const processPlaidAccount = async (accountData: ProcessPlaidAccountRequest): Promise<ProcessPlaidAccountResponse> => {
  return apiRequest<ProcessPlaidAccountResponse>({
    method: "POST",
    url: "/merchants/plaid/process-account",
    data: accountData,
  });
};
