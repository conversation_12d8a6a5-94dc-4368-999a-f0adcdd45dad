import { apiRequest, handleApiResponse } from "../utils/apiRequest";
import { apiClient } from "../config";
import type {
  GenerateIntegrationTokenRequest,
  GenerateIntegrationTokenResponse,
  ValidateIframeTokenResponse,
  TokenPaymentRequest,
  TokenPaymentResponse,
} from "../types/payment";

export const generateIntegrationToken = async (tokenData: GenerateIntegrationTokenRequest): Promise<GenerateIntegrationTokenResponse> => {
  return apiRequest<GenerateIntegrationTokenResponse>({
    method: "POST",
    url: "/payments/generate-integration-token",
    data: tokenData,
  });
};

export const validateIframeToken = async (token: string, paymentMethod?: "card" | "google_pay"): Promise<ValidateIframeTokenResponse> => {
  return apiRequest<ValidateIframeTokenResponse>({
    method: "POST",
    url: "/payments/validate-iframe-token",
    data: {
      token,
      paymentMethod,
    },
  });
};

export const processTokenPayment = async (paymentData: TokenPaymentRequest): Promise<TokenPaymentResponse> => {
  const result = await handleApiResponse<TokenPaymentResponse>(
    apiClient.post("/payments/process-token-payment", paymentData)
  );
  
  if ("success" in result && !result.success) {
    return result as TokenPaymentResponse;
  }
  
  return result as TokenPaymentResponse;
};

export const cleanupIntegrationToken = async (
  token: string
): Promise<{ success: boolean; data?: { used: boolean; deleted: boolean }; message?: string }> => {
  return apiRequest<{ success: boolean; data?: { used: boolean; deleted: boolean }; message?: string }>({
    method: "POST",
    url: "/payments/cleanup-integration-token",
    data: { token },
  });
};
