import type { AxiosResponse, AxiosError } from "axios";
import { apiClient } from "../config";

const DEFAULT_ERROR_MESSAGE = "An error occurred while processing your request";
const NETWORK_ERROR_MESSAGE = "Network error occurred. Please check your connection.";
const TIMEOUT_ERROR_MESSAGE = "Request timed out. Please try again.";

interface ApiRequestOptions {
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  url: string;
  data?: unknown;
  headers?: Record<string, string>;
  timeout?: number;
}

interface ApiErrorResponse {
  success: false;
  message: string;
  error?: string;
  statusCode?: number;
}

export const apiRequest = async <T>(options: ApiRequestOptions): Promise<T> => {
  try {
    const response: AxiosResponse<T> = await apiClient.request({
      method: options.method,
      url: options.url,
      data: options.data,
      headers: options.headers,
      timeout: options.timeout,
    });
    return response.data;
  } catch (error) {
    throw formatApiError(error);
  }
};

export const formatApiError = (error: unknown): ApiErrorResponse => {
  if (!error) {
    return {
      success: false,
      message: DEFAULT_ERROR_MESSAGE,
    };
  }

  const axiosError = error as AxiosError<{ message?: string; error?: string }>;

  if (axiosError.code === "ECONNABORTED") {
    return {
      success: false,
      message: TIMEOUT_ERROR_MESSAGE,
      error: "TIMEOUT",
      statusCode: 408,
    };
  }

  if (axiosError.code === "ERR_NETWORK") {
    return {
      success: false,
      message: NETWORK_ERROR_MESSAGE,
      error: "NETWORK_ERROR",
      statusCode: 0,
    };
  }

  if (axiosError.response?.data) {
    return {
      success: false,
      message: axiosError.response.data.message || DEFAULT_ERROR_MESSAGE,
      error: axiosError.response.data.error || axiosError.message,
      statusCode: axiosError.response.status,
    };
  }

  if (axiosError.request) {
    return {
      success: false,
      message: NETWORK_ERROR_MESSAGE,
      error: "NO_RESPONSE",
      statusCode: 0,
    };
  }

  return {
    success: false,
    message: axiosError.message || DEFAULT_ERROR_MESSAGE,
    error: "UNKNOWN_ERROR",
  };
};

export const handleApiResponse = async <T>(
  promise: Promise<AxiosResponse<T>>
): Promise<T | ApiErrorResponse> => {
  try {
    const response = await promise;
    return response.data;
  } catch (error) {
    return formatApiError(error);
  }
};