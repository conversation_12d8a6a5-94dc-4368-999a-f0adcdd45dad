export const API_ENDPOINTS = {
  generateToken: {
    title: "Generate Integration Token",
    endpoint: "POST /payments/generate-integration-token",
    description: "Generates a secure token for iframe payment integration.",
    requestBody: `{
  "merchantId": "string (required)",
  "description": "string (required)",
  "amount": "number (optional, in cents)",
  "returnUrl": "string (optional)",
  "expiresIn": "number (optional, minutes, default: 60)"
}`,
    response: `{
  "success": true,
  "message": "Integration token generated successfully",
  "data": {
    "token": "secure-token-string",
    "expiresAt": "2024-01-01T12:00:00.000Z",
    "embedUrl": "https://your-domain.com/payment-iframe?token=...",
    "merchantInfo": {
      "id": "merchant-id",
      "name": "Merchant Name",
      "status": 1
    }
  }
}`,
  },

  validateToken: {
    title: "Validate Iframe Token",
    endpoint: "POST /payments/validate-iframe-token",
    description: "Validates a token and returns payment configuration for iframe.",
    requestBody: `{
  "token": "string (required)"
}`,
    response: `{
  "success": true,
  "message": "Token validated successfully",
  "data": {
    "config": {
      "merchantId": "merchant-id",
      "publicKey": "test-public-key",
      "amount": 2500,
      "description": "Product Purchase",
      "mode": "txn",
      "txnType": "sale"
    },
    "merchantInfo": {
      "id": "merchant-id",
      "name": "Merchant Name",
      "status": 1
    },
    "paymentInfo": {
      "description": "Product Purchase",
      "amount": 2500,
      "returnUrl": "https://yoursite.com/success"
    }
  }
}`,
  },

  cleanupToken: {
    title: "Cleanup Integration Token",
    endpoint: "POST /payments/cleanup-integration-token",
    description: "Marks an integration token as used and cleans it up after successful payment.",
    requestBody: `{
  "token": "string (required)"
}`,
    response: `{
  "success": true,
  "message": "Token marked as used and cleaned up successfully",
  "data": {
    "used": true,
    "deleted": true
  }
}`,
  },

  tokenStatus: {
    title: "Check Token Status",
    endpoint: "GET /payments/token-status?token=TOKEN",
    description: "Checks the status and validity of a payment token.",
    response: `{
  "success": true,
  "data": {
    "isValid": true,
    "status": "valid",
    "expiresAt": "2024-01-01T12:00:00.000Z",
    "timeRemaining": 3600,
    "merchantId": "merchant-id",
    "amount": 2500,
    "description": "Product Purchase"
  }
}`,
  },
};
