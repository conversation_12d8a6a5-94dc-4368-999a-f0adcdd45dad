import { ContentCard } from "../../../components/common/ContentCard";
import { BENEFITS_LIST, HOW_IT_WORKS_STEPS } from "../constants/navigationSections";

export const OverviewSection = () => {
  return (
    <ContentCard id="what-is-iframe" title="What is Iframe Integration?" variant="highlight">
      <p className="text-slate-600 text-lg leading-relaxed mb-6">
        Our iframe integration allows you to embed secure payment forms directly into your website without redirecting users to external payment
        pages. This provides a seamless user experience while maintaining PCI compliance and security.
      </p>

      <div className="grid sm:grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-xl p-6 lg:p-8">
          <h3 className="text-lg font-semibold text-slate-800 mb-6 flex items-center">
            <svg className="w-6 h-6 mr-3 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Benefits
          </h3>
          <ul className="space-y-4 text-slate-700">
            {BENEFITS_LIST.map((benefit, index) => (
              <li key={index} className="flex items-start">
                <svg className="w-5 h-5 text-slate-600 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span className="text-sm leading-relaxed">{benefit}</span>
              </li>
            ))}
          </ul>
        </div>

        <div className="bg-gradient-to-br from-gray-50 to-slate-50 border border-gray-200 rounded-xl p-6 lg:p-8">
          <h3 className="text-lg font-semibold text-slate-800 mb-6 flex items-center">
            <svg className="w-6 h-6 mr-3 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            How It Works
          </h3>
          <ol className="space-y-4 text-slate-700">
            {HOW_IT_WORKS_STEPS.map((step, index) => (
              <li key={index} className="flex items-start">
                <span className="bg-slate-800 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0 font-semibold">
                  {index + 1}
                </span>
                <span className="text-sm leading-relaxed">{step}</span>
              </li>
            ))}
          </ol>
        </div>
      </div>
    </ContentCard>
  );
};