import { ContentCard } from "../../../components/common/ContentCard";
import { TOKEN_SECURITY_FEATURES, PCI_COMPLIANCE_FEATURES } from "../constants/navigationSections";

export const SecuritySection = () => {
  return (
    <ContentCard id="security" title="Security Features" variant="warning">
      <p className="text-slate-700 mb-6 leading-relaxed">
        Our iframe integration is designed with security as a top priority. All sensitive payment data is processed within our secure environment
        and never touches your servers.
      </p>
      <div className="grid sm:grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white/50 rounded-lg p-6">
          <h4 className="font-semibold text-slate-800 mb-4 flex items-center">
            <svg className="w-5 h-5 mr-3 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"
              />
            </svg>
            Token Security
          </h4>
          <ul className="space-y-3 text-slate-700">
            {TOKEN_SECURITY_FEATURES.map((item, index) => (
              <li key={index} className="flex items-start">
                <svg className="w-4 h-4 text-slate-600 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span className="text-sm leading-relaxed">{item}</span>
              </li>
            ))}
          </ul>
        </div>
        <div className="bg-white/50 rounded-lg p-6">
          <h4 className="font-semibold text-slate-800 mb-4 flex items-center">
            <svg className="w-5 h-5 mr-3 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
              />
            </svg>
            PCI Compliance
          </h4>
          <ul className="space-y-3 text-slate-700">
            {PCI_COMPLIANCE_FEATURES.map((item, index) => (
              <li key={index} className="flex items-start">
                <svg className="w-4 h-4 text-slate-600 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span className="text-sm leading-relaxed">{item}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </ContentCard>
  );
};