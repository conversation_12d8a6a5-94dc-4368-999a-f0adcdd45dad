export const TEST_MERCHANT_DATA = {
  BASIC: {
    dba: "Tech Solutions",
    legalName: "Tech Solutions LLC",
    ownership: 0,
    annualVolume: 1000000,
    avgTransactionAmount: 150,
    public: 0,
    website: "https://techsolutions.com",
    email: "<EMAIL>",
    address1: "123 Main Street",
    address2: "Suite 100",
    city: "San Francisco",
    state: "CA",
    zip: "94102",
    phone: "4155551234",
    customerPhone: "4155551234",
    tcVersion: "072023",
  },
  EIN: "123456789",
  MCC: "5812",
  ESTABLISHED_DATE: "20200101",
  DESCRIPTIONS: {
    SHORT: "Tech consulting services",
    LONG: "We provide comprehensive technology consulting services to businesses",
    PRODUCT: "Software development and IT consulting",
  },
} as const;

export const TEST_OWNER_DATA = {
  OWNER_1: {
    ssn: "123456789",
    dob: "19800101",
    firstName: "<PERSON>",
    lastName: "<PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "4155559876",
    address: {
      address1: "123 Main Street",
      address2: "Suite 100",
      city: "San Francisco",
      state: "CA",
      zip: "94102",
    },
    ownership: 60,
    title: "CEO",
    principal: 1,
  },
  OWNER_2: {
    ssn: "*********",
    dob: "********",
    firstName: "Jane",
    lastName: "Smith",
    email: "<EMAIL>",
    phone: "**********",
    address: {
      address1: "456 Market Street",
      address2: "",
      city: "San Francisco",
      state: "CA",
      zip: "94103",
    },
    ownership: 40,
    title: "CTO",
    principal: 1,
  },
} as const;

export const TEST_BANK_DATA = {
  ACCOUNT_NUMBER: "**********",
  ROUTING_NUMBER: "*********",
  TYPE: "checking" as const,
} as const;

export const TEST_COMPLIANCE_POLICIES = {
  returnRefundPolicy: {
    type: "return_refund" as const,
    title: "Return and Refund Policy",
    content: "We offer a 30-day return policy for all items. Items must be in original condition. Refunds will be processed within 5-7 business days after we receive the returned item. Shipping costs are non-refundable unless the item was defective or incorrectly shipped.",
    url: "https://example.com/return-policy",
    version: "1.0",
    lastUpdated: new Date().toISOString().split("T")[0],
  },
  deliveryPolicy: {
    type: "delivery" as const,
    title: "Delivery Policy",
    content: "Standard shipping takes 3-5 business days. Express shipping available for next-day delivery. We ship Monday through Friday. Delivery confirmation required for orders over $100. International shipping available to select countries with additional fees.",
    url: "https://example.com/delivery-policy",
    version: "1.0",
    lastUpdated: new Date().toISOString().split("T")[0],
  },
  privacyPolicy: {
    type: "privacy" as const,
    title: "Privacy Policy",
    content: "We collect and protect your personal information in accordance with applicable privacy laws. Your payment information is encrypted and securely processed. We do not sell or share your personal data with third parties except as necessary to complete your transaction.",
    url: "https://example.com/privacy-policy",
    version: "1.0",
    lastUpdated: new Date().toISOString().split("T")[0],
  },
  securityPolicy: {
    type: "security" as const,
    title: "Secure Checkout Policy",
    content: "Our checkout process uses industry-standard SSL encryption to protect your payment information. All transactions are processed through PCI-compliant payment processors. Your credit card information is never stored on our servers.",
    url: "https://example.com/security-policy",
    version: "1.0",
    lastUpdated: new Date().toISOString().split("T")[0],
  },
  termsAndConditions: {
    type: "terms" as const,
    title: "Terms and Conditions",
    content: "By making a purchase, you agree to our terms of service. All sales are final unless covered by our return policy. We reserve the right to refuse service. Prices are subject to change without notice. These terms are governed by applicable local laws.",
    url: "https://example.com/terms",
    version: "1.0",
    lastUpdated: new Date().toISOString().split("T")[0],
  },
} as const;