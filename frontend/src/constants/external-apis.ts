export const IP_DETECTION_SERVICES = [
  {
    name: "ipify",
    url: "https://api.ipify.org?format=json",
    extractIp: (data: Record<string, unknown>) => String(data.ip || ""),
  },
  {
    name: "ipapi",
    url: "https://ipapi.co/json/",
    extractIp: (data: Record<string, unknown>) => String(data.ip || ""),
  },
  {
    name: "httpbin",
    url: "https://httpbin.org/ip",
    extractIp: (data: Record<string, unknown>) => String(data.origin || ""),
  },
] as const;

export const DEFAULT_IP = "127.0.0.1";

export const IP_DETECTION_CONFIG = {
  DEFAULT_IP,
  SERVICES: IP_DETECTION_SERVICES,
  RETRY_ATTEMPTS: 3,
} as const;