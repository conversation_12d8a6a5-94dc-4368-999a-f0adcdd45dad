export const ICON_SIZES = {
  XS: "w-4 h-4",
  SM: "w-6 h-6",
  MD: "w-8 h-8",
  LG: "w-12 h-12",
  XL: "w-16 h-16",
} as const;

export const SVG_VIEWBOX = {
  STANDARD: "0 0 24 24",
  SMALL: "0 0 20 20",
  GOOGLE_PAY: "0 0 41 17",
} as const;

export const BUTTON_STYLES = {
  PRIMARY: "px-6 py-3 bg-slate-900 text-white rounded-lg hover:bg-slate-800 transition-colors",
  PRIMARY_LARGE: "px-8 py-4 bg-slate-900 text-white rounded-lg hover:bg-slate-800 transition-colors text-lg font-medium shadow-lg transform hover:scale-105",
  SECONDARY: "px-6 py-3 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors",
  SECONDARY_LARGE: "px-8 py-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-lg font-medium shadow-lg transform hover:scale-105",
  BLOCK: "block w-full",
} as const;

export const INPUT_STYLES = {
  BASE: "w-full px-4 py-3 border rounded-lg transition-colors",
  FOCUS: "focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
  ERROR: "border-red-300 bg-red-50",
  DISABLED: "border-gray-200 bg-gray-100 text-gray-500 cursor-not-allowed",
  NORMAL: "border-gray-300",
} as const;

export const CONTAINER_STYLES = {
  FULL_SCREEN: "min-h-screen",
  CENTER: "flex items-center justify-center",
  MAX_WIDTH_SM: "max-w-sm",
  MAX_WIDTH_MD: "max-w-md",
  MAX_WIDTH_LG: "max-w-lg",
  MAX_WIDTH_XL: "max-w-xl",
  MAX_WIDTH_6XL: "max-w-6xl mx-auto px-4",
} as const;

export const SPACING = {
  PAGE_PADDING: "px-6",
  SECTION_PADDING: "px-8 py-6",
  CARD_PADDING: "p-6",
  BUTTON_PADDING: "px-6 py-3",
  INPUT_PADDING: "px-4 py-3",
  SMALL_GAP: "gap-2",
  MEDIUM_GAP: "gap-4",
  LARGE_GAP: "gap-6",
} as const;

export const COLORS = {
  BACKGROUND: {
    PRIMARY: "bg-gray-50",
    SECONDARY: "bg-slate-50",
    GRADIENT: "bg-gradient-to-br from-slate-50 to-gray-50",
  },
  BORDER: {
    DEFAULT: "border-gray-300",
    LIGHT: "border-gray-200",
    ERROR: "border-red-300",
    SUCCESS: "border-green-300",
    INFO: "border-blue-200",
  },
  TEXT: {
    PRIMARY: "text-gray-900",
    SECONDARY: "text-gray-700",
    MUTED: "text-gray-500",
    ERROR: "text-red-600",
    SUCCESS: "text-green-600",
  },
} as const;

export const TRANSITIONS = {
  DEFAULT: "transition-colors",
  ALL: "transition-all",
  DURATION_150: "duration-150",
  DURATION_300: "duration-300",
  EASE_IN_OUT: "ease-in-out",
} as const;