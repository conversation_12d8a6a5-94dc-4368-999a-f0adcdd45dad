export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^\d{10}$/,
  ZIP: /^\d{5}(-\d{4})?$/,
  SSN: /^\d{9}$/,
  EIN: /^\d{9}$/,
  URL: /^https?:\/\/[^\s]+$/,
  DIGITS_ONLY: /\D/g,
} as const;

export const FIELD_LENGTHS = {
  SSN: {
    MIN: 9,
    MAX: 11,
    DISPLAY: 11,
  },
  EIN: {
    MIN: 9,
    MAX: 10,
    DISPLAY_SOLE: 11,
    DISPLAY_BUSINESS: 10,
  },
  PHONE: {
    MIN: 10,
    MAX: 14,
    DISPLAY: 14,
  },
  ZIP: {
    MIN: 5,
    MAX: 10,
  },
  ROUTING_NUMBER: {
    MIN: 9,
    MAX: 9,
  },
  ACCOUNT_NUMBER: {
    MIN: 4,
    MAX: 17,
  },
  YEAR: {
    LENGTH: 4,
  },
  MONTH: {
    MIN: 1,
    MAX: 12,
  },
  DAY: {
    MIN: 1,
    MAX: 31,
  },
} as const;

export const VALIDATION_MESSAGES = {
  REQUIRED: (field: string) => `${field} is required`,
  MIN_LENGTH: (field: string, min: number) => `${field} must be at least ${min} characters`,
  MAX_LENGTH: (field: string, max: number) => `${field} must be no more than ${max} characters`,
  INVALID_FORMAT: (field: string) => `Invalid ${field} format`,
  INVALID_EMAIL: "Invalid email address",
  INVALID_URL: "Invalid URL format",
  INVALID_PHONE: "Invalid phone number",
  INVALID_SSN: "Invalid SSN format",
  INVALID_EIN: "Invalid EIN format",
  INVALID_ZIP: "Invalid ZIP code",
  INVALID_ROUTING: "Invalid routing number",
  INVALID_ACCOUNT: "Invalid account number",
  INVALID_DATE: "Invalid date",
  DATE_IN_FUTURE: "Date cannot be in the future",
  MUST_BE_18: "Must be at least 18 years old",
} as const;

export const AMOUNT_LIMITS = {
  MIN_CENTS: 50,
  MAX_CENTS: ********,
  MIN_DOLLARS: 0.5,
  MAX_DOLLARS: 100000,
  CENTS_PER_DOLLAR: 100,
} as const;