#!/bin/bash

# Script to update CloudFront distribution ID in package.json
# Usage: ./update-cloudfront-id.sh [stage] [profile]

STAGE=${1:-dev}
PROFILE=${2:-payrix}

echo "Getting CloudFront distribution ID for stage: $STAGE"

# Get account ID for ARN construction
ACCOUNT_ID=$(aws sts get-caller-identity --profile $PROFILE --query Account --output text)

# Get all CloudFront distributions
DISTRIBUTIONS=$(aws cloudfront list-distributions --profile $PROFILE --output json | jq -r '.DistributionList.Items[] | .Id')

# Find the distribution with matching STAGE tag
DISTRIBUTION_ID=""
DISTRIBUTION_DOMAIN=""

for dist_id in $DISTRIBUTIONS; do
    echo "Checking distribution: $dist_id"
    STAGE_TAG=$(aws cloudfront list-tags-for-resource --resource "arn:aws:cloudfront::$ACCOUNT_ID:distribution/$dist_id" --profile $PROFILE --output json | jq -r '.Tags.Items[] | select(.Key=="STAGE") | .Value')

    if [ "$STAGE_TAG" == "$STAGE" ]; then
        echo "Found matching distribution for stage $STAGE: $dist_id"
        DISTRIBUTION_ID=$dist_id
        DISTRIBUTION_DOMAIN=$(aws cloudfront list-distributions --profile $PROFILE --output json | jq -r ".DistributionList.Items[] | select(.Id==\"$dist_id\") | .DomainName")
        break
    fi
done

if [ -z "$DISTRIBUTION_ID" ]; then
    echo "Error: Could not find CloudFront distribution for stage: $STAGE"
    echo "Available stages:"
    for dist_id in $DISTRIBUTIONS; do
        STAGE_TAG=$(aws cloudfront list-tags-for-resource --resource "arn:aws:cloudfront::$ACCOUNT_ID:distribution/$dist_id" --profile $PROFILE --output json | jq -r '.Tags.Items[] | select(.Key=="STAGE") | .Value')
        if [ -n "$STAGE_TAG" ]; then
            echo "  - $STAGE_TAG (Distribution: $dist_id)"
        fi
    done
    exit 1
fi

echo "Found CloudFront distribution:"
echo "  ID: $DISTRIBUTION_ID"
echo "  Domain: $DISTRIBUTION_DOMAIN"

# Update package.json with the correct distribution ID based on stage
if [ "$STAGE" == "dev" ]; then
    # Update the base invalidate command for dev
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/\"invalidate\": \"aws cloudfront create-invalidation --distribution-id [A-Z0-9]*/\"invalidate\": \"aws cloudfront create-invalidation --distribution-id $DISTRIBUTION_ID/g" package.json
    else
        # Linux
        sed -i "s/\"invalidate\": \"aws cloudfront create-invalidation --distribution-id [A-Z0-9]*/\"invalidate\": \"aws cloudfront create-invalidation --distribution-id $DISTRIBUTION_ID/g" package.json
    fi
elif [ "$STAGE" == "prod" ]; then
    # Update the invalidate:prod command for prod
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/\"invalidate:prod\": \"aws cloudfront create-invalidation --distribution-id [A-Z0-9]*/\"invalidate:prod\": \"aws cloudfront create-invalidation --distribution-id $DISTRIBUTION_ID/g" package.json
    else
        # Linux
        sed -i "s/\"invalidate:prod\": \"aws cloudfront create-invalidation --distribution-id [A-Z0-9]*/\"invalidate:prod\": \"aws cloudfront create-invalidation --distribution-id $DISTRIBUTION_ID/g" package.json
    fi
else
    echo "Warning: Unknown stage '$STAGE'. No package.json updates made."
fi

echo "Updated package.json with distribution ID: $DISTRIBUTION_ID"

# Output both values for easy copying
echo ""
echo "=== CloudFront Information ==="
echo "Distribution ID: $DISTRIBUTION_ID"
echo "Domain: $DISTRIBUTION_DOMAIN"
echo "Full URL: https://$DISTRIBUTION_DOMAIN" 