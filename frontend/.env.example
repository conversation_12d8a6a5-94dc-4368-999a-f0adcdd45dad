# ==========================================
# ENVIRONMENT VARIABLES TEMPLATE
# Copy this file to .env and update values
# ==========================================

# ==========================================
# ACTIVE ENVIRONMENT VARIABLES
# ==========================================

# API Configuration
# Used in: src/services/config.ts
# Purpose: Base URL for all API calls to the backend
# Local: http://localhost:3000/dev
# Production: https://7p1bwpmt12.execute-api.us-east-1.amazonaws.com/dev
VITE_API_URL=http://localhost:3000/dev

# Environment Indicator
# Used in: src/components/common/EnvironmentBanner.tsx
#          src/components/layouts/AppLayout.tsx
#          src/components/Navigation.tsx
# Purpose: Shows environment banner and adjusts UI layout
# Values: dev | staging | production
VITE_ENVIRONMENT=dev

# Payrix Payment Configuration
# Used in: src/components/payments/utils/script-loader.ts
# Purpose: URL to load PayFields payment processing script
# Test: https://test-api.payrix.com/payFieldsScript?spa=1&iframe=1
# Production: https://api.payrix.com/payFieldsScript?spa=1&iframe=1
VITE_PAYRIX_PAYMENT_URL=https://test-api.payrix.com/payFieldsScript?spa=1&iframe=1
