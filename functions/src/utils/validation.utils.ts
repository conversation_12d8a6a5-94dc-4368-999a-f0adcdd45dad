import { HTTP_STATUS } from "../constants/http.constants.js";
import { VALIDATION_ERROR_MESSAGES } from "../constants/error-messages.constants.js";

export interface ValidationResult {
  isValid: boolean;
  statusCode?: number;
  error?: string;
  message?: string;
  details?: Record<string, unknown>;
}

const TOKEN_PATTERNS = {
  HEX_32: /^[a-f0-9]{32}$/i,
  HEX_64: /^[a-f0-9]{64}$/i,
} as const;

const MERCHANT_ID_PATTERN = /^[a-zA-Z0-9_-]{1,50}$/;
const URL_HTTPS_PROTOCOL = "https:";

const VALIDATION_LIMITS = {
  DESCRIPTION_MIN: 3,
  DESCRIPTION_MAX: 255,
  INPUT_MAX: 500,
  AMOUNT_MIN: 50,
  AMOUNT_MAX: 10000000,
} as const;

export function sanitizeInput(input: string): string {
  if (!input || typeof input !== "string") {
    return "";
  }

  return input
    .replace(/[<>"'&]/g, "")
    .replace(/\s+/g, " ")
    .trim()
    .substring(0, VALIDATION_LIMITS.INPUT_MAX);
}

export function validateTokenFormat(token: string): ValidationResult {
  if (!token || typeof token !== "string") {
    return {
      isValid: false,
      statusCode: HTTP_STATUS.BAD_REQUEST,
      error: "Invalid token format",
      message: "Token must be a non-empty string",
    };
  }

  if (!TOKEN_PATTERNS.HEX_32.test(token) && !TOKEN_PATTERNS.HEX_64.test(token)) {
    return {
      isValid: false,
      statusCode: HTTP_STATUS.BAD_REQUEST,
      error: "Invalid token format",
      message: VALIDATION_ERROR_MESSAGES.TOKEN_32_OR_64_HEX,
    };
  }

  return { isValid: true };
}

export function validateMerchantIdFormat(merchantId: string): ValidationResult {
  if (!merchantId || typeof merchantId !== "string") {
    return {
      isValid: false,
      statusCode: HTTP_STATUS.BAD_REQUEST,
      error: "Invalid merchant ID",
      message: "Merchant ID is required and must be a string",
    };
  }

  if (!MERCHANT_ID_PATTERN.test(merchantId)) {
    return {
      isValid: false,
      statusCode: HTTP_STATUS.BAD_REQUEST,
      error: "Invalid merchant ID format",
      message: VALIDATION_ERROR_MESSAGES.MERCHANT_ID_FORMAT,
    };
  }

  return { isValid: true };
}

export function validateDescription(description: string): ValidationResult {
  if (!description || typeof description !== "string") {
    return {
      isValid: false,
      statusCode: HTTP_STATUS.BAD_REQUEST,
      error: "Invalid description",
      message: "Description is required and must be a string",
    };
  }

  const trimmedDescription = description.trim();
  if (trimmedDescription.length < VALIDATION_LIMITS.DESCRIPTION_MIN) {
    return {
      isValid: false,
      statusCode: HTTP_STATUS.BAD_REQUEST,
      error: "Description too short",
      message: `Description must be at least ${VALIDATION_LIMITS.DESCRIPTION_MIN} characters long`,
    };
  }

  if (trimmedDescription.length > VALIDATION_LIMITS.DESCRIPTION_MAX) {
    return {
      isValid: false,
      statusCode: HTTP_STATUS.BAD_REQUEST,
      error: "Description too long",
      message: `Description must not exceed ${VALIDATION_LIMITS.DESCRIPTION_MAX} characters`,
    };
  }

  return { isValid: true };
}

export function validateAmount(amount: number): ValidationResult {
  if (typeof amount !== "number" || isNaN(amount)) {
    return {
      isValid: false,
      statusCode: HTTP_STATUS.BAD_REQUEST,
      error: "Invalid amount",
      message: "Amount must be a valid number",
    };
  }

  if (amount < VALIDATION_LIMITS.AMOUNT_MIN) {
    return {
      isValid: false,
      statusCode: HTTP_STATUS.BAD_REQUEST,
      error: "Amount too low",
      message: `${VALIDATION_ERROR_MESSAGES.AMOUNT_MIN} (${VALIDATION_LIMITS.AMOUNT_MIN} cents)`,
    };
  }

  if (amount > VALIDATION_LIMITS.AMOUNT_MAX) {
    return {
      isValid: false,
      statusCode: HTTP_STATUS.BAD_REQUEST,
      error: "Amount too high",
      message: `${VALIDATION_ERROR_MESSAGES.AMOUNT_MAX} (${VALIDATION_LIMITS.AMOUNT_MAX} cents)`,
    };
  }

  return { isValid: true };
}

export function validateReturnUrl(url: string): ValidationResult {
  if (!url || typeof url !== "string") {
    return {
      isValid: false,
      statusCode: HTTP_STATUS.BAD_REQUEST,
      error: "Invalid return URL",
      message: "Return URL is required",
    };
  }

  try {
    const urlObj = new URL(url);
    if (urlObj.protocol !== URL_HTTPS_PROTOCOL) {
      return {
        isValid: false,
        statusCode: HTTP_STATUS.BAD_REQUEST,
        error: "Invalid return URL protocol",
        message: "Return URL must use HTTPS protocol for security",
      };
    }
  } catch {
    return {
      isValid: false,
      statusCode: HTTP_STATUS.BAD_REQUEST,
      error: "Invalid return URL format",
      message: "Return URL must be a valid URL",
    };
  }

  return { isValid: true };
}

export function validateExpiresIn(expiresIn: number | undefined): ValidationResult {
  if (expiresIn === undefined) {
    return { isValid: true };
  }

  if (typeof expiresIn !== "number" || isNaN(expiresIn) || expiresIn <= 0) {
    return {
      isValid: false,
      statusCode: HTTP_STATUS.BAD_REQUEST,
      error: "Invalid expiration time",
      message: "Expiration time must be a positive number",
    };
  }

  return { isValid: true };
}