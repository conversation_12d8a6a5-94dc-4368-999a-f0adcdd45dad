import { IntegrationTokenResponse, GooglePayConfig, CompliancePolicies } from "../../../types/integration-token.types.js";
import { DEFAULT_FRONTEND_URL } from "../../../constants/http.constants.js";

export function buildEmbedUrl(token: string): string {
  const baseUrl = process.env.FRONTEND_URL || DEFAULT_FRONTEND_URL;
  return `${baseUrl}/payment-iframe?token=${token}`;
}

export function buildTokenResponse(
  token: string,
  expiresAt: Date,
  merchantInfo: {
    id: string;
    name?: string;
    status?: number;
  }
): IntegrationTokenResponse {
  return {
    token,
    expiresAt: expiresAt.toISOString(),
    embedUrl: buildEmbedUrl(token),
    merchantInfo: {
      id: merchantInfo.id,
      name: merchantInfo.name || "Unknown",
      status: merchantInfo.status || 0,
    },
  };
}

export function buildTokenData(
  merchantId: string,
  description: string,
  amount: number,
  expiresAt: Date,
  returnUrl?: string,
  ecommerceData?: {
    currency?: string;
    items?: Array<{
      name: string;
      description?: string;
      quantity: number;
      unitPrice: number;
      total: number;
      commodityCode?: string;
      productCode?: string;
    }>;
    taxAmount?: number;
    shippingAmount?: number;
    dutyAmount?: number;
    orderNumber?: string;
    invoiceNumber?: string;
    customerCode?: string;
    orderDiscount?: number;
  },
  merchantInfo?: {
    address?: {
      line1?: string;
      line2?: string;
      city?: string;
      state?: string;
      zip?: string;
      country?: string;
    };
    email?: string;
    phone?: string;
  },
  walletConfig?: {
    googlePayConfig?: GooglePayConfig;
    enableDigitalWallets?: boolean;
  },
  compliancePolicies?: CompliancePolicies
) {
  return {
    merchantId,
    description,
    amount,
    returnUrl,
    expiresAt,
    used: false,
    currency: ecommerceData?.currency,
    items: ecommerceData?.items,
    taxAmount: ecommerceData?.taxAmount,
    shippingAmount: ecommerceData?.shippingAmount,
    dutyAmount: ecommerceData?.dutyAmount,
    orderNumber: ecommerceData?.orderNumber,
    invoiceNumber: ecommerceData?.invoiceNumber,
    customerCode: ecommerceData?.customerCode,
    orderDiscount: ecommerceData?.orderDiscount,
    compliancePolicies,
    googlePayConfig: walletConfig?.googlePayConfig,
    enableDigitalWallets: walletConfig?.enableDigitalWallets,
    merchantInfo: merchantInfo
      ? {
          address: merchantInfo.address,
          contactEmail: merchantInfo.email,
          contactPhone: merchantInfo.phone,
        }
      : undefined,
  };
}
