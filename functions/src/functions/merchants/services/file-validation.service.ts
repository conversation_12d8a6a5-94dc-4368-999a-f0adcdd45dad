import { logger } from "../../../helpers/logger.js";
import { VerificationFile } from "./bank-verification.service.js";
import { FILE_VALIDATION, FILE_ERROR_MESSAGES } from "../../../constants/file.constants.js";

const ALLOWED_FILE_TYPES = FILE_VALIDATION.ALLOWED_TYPES;
const MAX_FILE_SIZE = FILE_VALIDATION.MAX_SIZE_BYTES;

export function validateVerificationFile(file: VerificationFile, requestId: string, entityId: string): void {
  if (!ALLOWED_FILE_TYPES.includes(file.type.toLowerCase())) {
    logger.error("Invalid file type for bank verification", {
      requestId,
      entityId,
      fileType: file.type,
      allowedTypes: ALLOWED_FILE_TYPES,
    });
    throw new Error(FILE_ERROR_MESSAGES.INVALID_TYPE);
  }

  if (file.size > MAX_FILE_SIZE) {
    logger.error("File size exceeds limit for bank verification", {
      requestId,
      entityId,
      fileSize: file.size,
      maxFileSize: MAX_FILE_SIZE,
    });
    throw new Error(FILE_ERROR_MESSAGES.SIZE_EXCEEDED);
  }
}
