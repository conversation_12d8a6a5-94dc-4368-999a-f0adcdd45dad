export const PAYRIX_MERCHANT_STATUS = {
  ACTIVE_STATUSES: [1, 2] as number[],
  INACTIVE: 0,
  FROZEN: 0,
} as const;

export const PAYRIX_PAYMENT = {
  TYPE_AUTH: "1",
  ORIGIN_API: "2",
  DEFAULT_DESCRIPTION: "Token-based payment",
} as const;

export const PAYRIX_ERROR_MESSAGES = {
  AUTHENTICATION_FAILED: "Payrix authentication failed. Please check API credentials.",
  REQUEST_TOO_LARGE: "Request too large for Payrix. Please reduce size.",
  TOO_MANY_REQUESTS: "Too many requests to Payrix. Please try again later.",
  SERVICE_UNAVAILABLE: "Payrix service temporarily unavailable. Please try again later.",
  INVALID_RESPONSE_STRUCTURE: "Invalid Payrix response structure: no entity data found",
  NO_LINK_TOKEN: "Invalid Payrix response structure: no link token found",
  INVALID_REQUEST_PARAMS: "Invalid request parameters for Plaid link token creation",
  PLAID_NOT_ENABLED: "Plaid integration not enabled for this account",
  NOTE_MISSING_ID: "Note response missing required ID field",
  NOTE_DOCUMENT_MISSING_ID: "Note document response missing required ID field",
  NOTE_NOT_FOUND: "Note not found. Please ensure the note exists before uploading documents.",
  FILE_TOO_LARGE: "File too large for Payrix. Please use a smaller file.",
  NO_ENTITY_ID: "Payrix response did not contain entity ID",
  NO_MERCHANT_ID: "Could not extract merchant ID from Payrix response",
  NO_LINK_TOKEN_RESPONSE: "Payrix response did not contain link token",
  NO_NOTE_ID: "Payrix response did not contain note ID",
  NO_DOCUMENT_ID: "Payrix response did not contain document ID",
  VERIFICATION_NOTE_FAILED: "Failed to create verification note - Payrix did not return note ID",
  NOTE_DOCUMENT_FAILED: "Failed to create note document - Payrix did not return document ID",
  API_KEY_REQUIRED: "PAYRIX_PRIVATE_API_KEY environment variable is required",
  ERROR_IN_3_STEP_PROCESS: "Error in createNoteDocument 3-step process",
} as const;

export const PAYRIX_VERIFICATION = {
  MANUAL_REQUIRES_FILE: "Manual verification requires a file upload",
  INVALID_DATA: "Invalid data provided for bank verification upload",
  AUTHENTICATION_FAILED: "Authentication failed with Payrix API",
} as const;