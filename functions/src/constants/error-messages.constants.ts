export const GENERIC_ERROR_MESSAGES = {
  TOKEN_REQUIRED: "Token is required",
  TOKEN_NOT_FOUND_OR_USED: "Token not found or already used",
  TOKEN_CLEANUP_FAILED: "Failed to cleanup token",
  INVALID_TOKEN_FORMAT: "Invalid token format",
  PROVIDE_TOKEN: "Please provide a token",
  NO_MEMBERS_FOUND: "No members found in merchant data to create user account",
  NO_PRIMARY_MEMBER: "No primary member found to create user account",
  ENTITY_ID_EXTRACTION_FAILED: "Could not extract entity ID from merchant creation response",
} as const;

export const VALIDATION_ERROR_MESSAGES = {
  TOKEN_32_OR_64_HEX: "Token must be a 32 or 64-character hexadecimal string",
  MERCHANT_ID_FORMAT: "Merchant ID must be alphanumeric with optional hyphens/underscores, 1-50 characters",
  AMOUNT_MIN: "Amount must be at least $0.50",
  AMOUNT_MAX: "Amount must not exceed $100,000",
} as const;