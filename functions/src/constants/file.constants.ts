export const FILE_VALIDATION = {
  ALLOWED_TYPES: ["image/jpeg", "image/jpg", "image/png", "application/pdf"] as string[],
  MAX_SIZE_BYTES: 10 * 1024 * 1024,
  MAX_SIZE_MB: 10,
} as const;

export const FILE_ERROR_MESSAGES = {
  INVALID_TYPE: "Invalid file type. Only JPEG, PNG, and PDF files are allowed for bank verification.",
  SIZE_EXCEEDED: "File size exceeds 10MB limit for bank verification documents.",
} as const;

export const TIME_CONVERSION = {
  MS_TO_SECONDS: 1000,
  SECONDS_TO_MS: 1000,
} as const;