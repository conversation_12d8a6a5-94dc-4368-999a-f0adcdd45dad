import { PayrixError } from "../../types/payrix.types.js";
import { logger } from "../../helpers/logger.js";
import { HTTP_STATUS } from "../../constants/http.constants.js";
import { PAYRIX_ERROR_MESSAGES } from "../../constants/payrix.constants.js";

interface PayrixResponse<T = unknown> {
  response?: {
    data?: T[];
    errors?: PayrixError[];
  };
  data?: T[] | T;
  errors?: PayrixError[];
  id?: string;
  message?: string;
}

export function parsePayrixResponse<T = unknown>(
  response: { data: PayrixResponse<T>; status: number },
  entityName: string
): T {
  const errors = response.data?.response?.errors || response.data?.errors;
  if (errors && Array.isArray(errors) && errors.length > 0) {
    const errorMessages = errors.map((err: PayrixError) => `${err.field || "general"}: ${err.msg}`).join(", ");
    logger.error(`Payrix API errors for ${entityName}`, { errors });
    throw new Error(`Payrix API errors: ${errorMessages}`);
  }

  let parsedData: T | undefined;

  if (response.data?.response?.data?.[0]) {
    parsedData = response.data.response.data[0];
  } else if (Array.isArray(response.data?.data) && response.data.data[0]) {
    parsedData = response.data.data[0];
  } else if (response.data?.id) {
    parsedData = response.data as T;
  } else if (response.data?.data && !Array.isArray(response.data.data)) {
    parsedData = response.data.data as T;
  }

  if (!parsedData) {
    logger.error(`Invalid Payrix response structure for ${entityName}`, {
      responseData: response.data,
      status: response.status,
    });
    throw new Error(`Invalid Payrix response structure: no ${entityName} data found`);
  }

  return parsedData;
}

export function parsePayrixErrorResponse(error: unknown, operation: string): string {
  if (error instanceof Error) {
    const axiosError = error as {
      response?: {
        status?: number;
        data?: PayrixResponse | { message?: string };
      };
      message: string;
    };

    if (axiosError.response?.data) {
      const data = axiosError.response.data;
      
      if ("message" in data && typeof data.message === "string") {
        return data.message;
      }

      const errors = (data as PayrixResponse).response?.errors || (data as PayrixResponse).errors;
      if (errors && Array.isArray(errors) && errors.length > 0) {
        return errors.map((err: PayrixError) => `${err.field || "general"}: ${err.msg}`).join(", ");
      }
    }

    return axiosError.message;
  }

  return `${operation} failed: Unknown error`;
}

export function handlePayrixStatusError(
  status: number | undefined,
  data: Record<string, unknown> | undefined,
  operation: string
): never {
  const message = (data?.message as string) || "";

  switch (status) {
    case HTTP_STATUS.BAD_REQUEST:
      throw new Error(`Payrix validation error: ${message || "Invalid request data"}`);
    case HTTP_STATUS.UNAUTHORIZED:
    case HTTP_STATUS.FORBIDDEN:
      throw new Error(PAYRIX_ERROR_MESSAGES.AUTHENTICATION_FAILED);
    case HTTP_STATUS.NOT_FOUND:
      throw new Error(`${operation} not found in Payrix.`);
    case HTTP_STATUS.REQUEST_TOO_LARGE:
      throw new Error(PAYRIX_ERROR_MESSAGES.REQUEST_TOO_LARGE);
    case HTTP_STATUS.UNPROCESSABLE_ENTITY:
      throw new Error(`Payrix processing error: ${message || "Unable to process request"}`);
    case HTTP_STATUS.TOO_MANY_REQUESTS:
      throw new Error(PAYRIX_ERROR_MESSAGES.TOO_MANY_REQUESTS);
    default:
      if (status && status >= HTTP_STATUS.INTERNAL_ERROR) {
        throw new Error(PAYRIX_ERROR_MESSAGES.SERVICE_UNAVAILABLE);
      }
      throw new Error(`Payrix error (${status}): ${message || "Unknown error"}`);
  }
}