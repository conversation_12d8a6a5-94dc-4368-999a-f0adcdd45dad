import axios, { AxiosInstance } from "axios";
import { PAYRIX_ERROR_MESSAGES } from "../../constants/payrix.constants.js";

const PAYRIX_API_URL = process.env.PAYRIX_API_URL || "";
const PAYRIX_PRIVATE_API_KEY = process.env.PAYRIX_PRIVATE_API_KEY;

if (!PAYRIX_PRIVATE_API_KEY) {
  throw new Error(PAYRIX_ERROR_MESSAGES.API_KEY_REQUIRED);
}

export function createPayrixApiClient(): AxiosInstance {
  return axios.create({
    baseURL: PAYRIX_API_URL,
    headers: {
      "Content-Type": "application/json",
      APIKEY: PAYRIX_PRIVATE_API_KEY,
    },
  });
}

export { PAYRIX_API_URL, PAYRIX_PRIVATE_API_KEY };
