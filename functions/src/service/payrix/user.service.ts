import { AxiosError } from "axios";
import { logger } from "../../helpers/logger.js";
import { PayrixUserResponse, UserAccountData } from "../../types/payrix.types.js";
import { createPayrixApiClient } from "./api-client.js";
import { parsePayrixResponse } from "./response-parser.js";

const apiClient = createPayrixApiClient();

export async function createUserAccount(userData: UserAccountData): Promise<PayrixUserResponse> {
  try {
    logger.info("Creating user account in Payrix", {
      originalUsername: userData.username,
      email: userData.email,
      merchantId: userData.merchantId,
    });

    const response = await apiClient.post("/logins", userData);

    logger.info("Payrix user account API response", {
      status: response.status,
      username: userData.username,
      data: response.data,
    });

    const loginData = parsePayrixResponse<PayrixUserResponse>(response, "user account");

    logger.info("Payrix user account created successfully", {
      status: response.status,
      originalUsername: userData.username,
      loginId: loginData.id,
    });

    return {
      ...loginData,
      originalUsername: userData.username,
    };
  } catch (error) {
    const axiosError = error as AxiosError;

    logger.error("Payrix User Account Creation Error", {
      originalUsername: userData.username,
      email: userData.email,
      status: axiosError.response?.status,
      statusText: axiosError.response?.statusText,
      data: axiosError.response?.data,
      message: axiosError.message,
    });

    throw new Error(
      `Payrix User Account Creation Error (${axiosError.response?.status}): ${JSON.stringify(axiosError.response?.data || axiosError.message)}`
    );
  }
}