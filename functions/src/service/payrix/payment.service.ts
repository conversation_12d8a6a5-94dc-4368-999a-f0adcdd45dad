import { AxiosError } from "axios";
import { PaymentResult, TokenDeletionResult, TokenPaymentData } from "../../types/payrix.types.js";
import { createPayrixApiClient } from "./api-client.js";
import { parsePayrixResponse, parsePayrixErrorResponse } from "./response-parser.js";

const apiClient = createPayrixApiClient();

const PAYMENT_DEFAULTS = {
  TYPE: "1",
  ORIGIN: "2",
  DEFAULT_DESCRIPTION: "Token-based payment",
} as const;

export async function processTokenPayment(paymentData: TokenPaymentData): Promise<PaymentResult> {
  try {
    const transactionData = {
      merchant: paymentData.merchantId,
      type: PAYMENT_DEFAULTS.TYPE,
      origin: PAYMENT_DEFAULTS.ORIGIN,
      token: paymentData.token,
      total: paymentData.amount.toString(),
      description: paymentData.description || PAYMENT_DEFAULTS.DEFAULT_DESCRIPTION,
      ...(paymentData.customerInfo?.email && { email: paymentData.customerInfo.email }),
      ...(paymentData.customerInfo?.name && { name: paymentData.customerInfo.name }),
      ...(paymentData.customerInfo?.address && {
        address1: paymentData.customerInfo.address.line1,
        address2: paymentData.customerInfo.address.line2,
        city: paymentData.customerInfo.address.city,
        state: paymentData.customerInfo.address.state,
        zip: paymentData.customerInfo.address.zip,
        country: paymentData.customerInfo.address.country,
      }),
    };

    const response = await apiClient.post("/txns", transactionData);
    const transactionResponse = parsePayrixResponse<Record<string, unknown>>(response, "transaction");

    return {
      success: true,
      transaction: transactionResponse,
    };
  } catch (error) {
    const errorMessage = parsePayrixErrorResponse(error, "Payment processing");
    
    return {
      success: false,
      error: errorMessage,
    };
  }
}

export async function cleanupToken(tokenId: string): Promise<TokenDeletionResult> {
  try {
    // Use the tokenId (format: t1_tok_*) for the DELETE /tokens/{id} API call
    await apiClient.delete(`/tokens/${tokenId}`);

    return {
      success: true,
      message: "Token deleted successfully",
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    return {
      success: false,
      error: `Token cleanup failed: ${axiosError.message}`,
    };
  }
}
