import { APIGatewayProxyEvent } from "aws-lambda";
import { logger } from "../helpers/logger.js";
import { CORS_ORIGINS } from "../constants/http.constants.js";

const ALLOWED_ORIGINS = CORS_ORIGINS;

const IFRAME_ALLOWED_ORIGINS = "*";

export function getCorsHeaders(event: APIGatewayProxyEvent, allowAllOrigins = false): Record<string, string> {
  const origin = event.headers?.origin || event.headers?.Origin || "";

  if (allowAllOrigins) {
    return {
      "Access-Control-Allow-Origin": IFRAME_ALLOWED_ORIGINS,
      "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
      "Access-Control-Allow-Methods": "OPTIONS,POST,GET",
      "Access-Control-Allow-Credentials": "true",
    };
  }

  if (ALLOWED_ORIGINS.includes(origin)) {
    return {
      "Access-Control-Allow-Origin": origin,
      "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
      "Access-Control-Allow-Methods": "OPTIONS,POST,GET",
      "Access-Control-Allow-Credentials": "true",
    };
  }

  logger.warn("CORS request from unauthorized origin", { origin });
  return {
    "Access-Control-Allow-Origin": ALLOWED_ORIGINS[0],
    "Access-Control-Allow-Headers": "Content-Type",
    "Access-Control-Allow-Methods": "OPTIONS,POST",
  };
}

export function isOriginAllowed(origin: string): boolean {
  return ALLOWED_ORIGINS.includes(origin);
}

export function getAllowedOrigins(): string[] {
  return [...ALLOWED_ORIGINS];
}
