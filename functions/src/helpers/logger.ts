import moment from "moment-timezone";

export enum LogLevel {
  ERROR = "ERROR",
  WARN = "WARN",
  INFO = "INFO",
  DEBUG = "DEBUG",
}

interface LogContext {
  [key: string]: unknown;
}

const colors = {
  reset: "\x1b[0m",
  red: "\x1b[31m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  gray: "\x1b[90m",
  green: "\x1b[32m",
} as const;

const levelColors = {
  [LogLevel.ERROR]: colors.red,
  [LogLevel.WARN]: colors.yellow,
  [LogLevel.INFO]: colors.blue,
  [LogLevel.DEBUG]: colors.gray,
} as const;

let context: LogContext = {};

export function setContext(newContext: LogContext): void {
  context = { ...context, ...newContext };
}

export function clearContext(): void {
  context = {};
}

function log(level: LogLevel, message: string, data?: LogContext): void {
  const timestamp = moment().tz("America/Chicago").format();
  const logEntry = {
    timestamp,
    level,
    message,
    ...context,
    ...data,
  };

  const color = levelColors[level];
  const coloredOutput = `${color}${JSON.stringify(logEntry)}${colors.reset}`;

  process.stdout.write(coloredOutput + "\n");
}

export const logger = {
  error: (message: string, data?: LogContext) => log(LogLevel.ERROR, message, data),
  warn: (message: string, data?: LogContext) => log(LogLevel.WARN, message, data),
  info: (message: string, data?: LogContext) => log(LogLevel.INFO, message, data),
  debug: (message: string, data?: LogContext) => log(LogLevel.DEBUG, message, data),
  setContext,
  clearContext,
};