# Production Deployment Information

## 🌐 Production URLs

### Frontend
- **CloudFront URL**: https://d2bpf0tsg3n3wi.cloudfront.net
- **Custom Domain (Future)**: https://app.auth-clear.com
- **CloudFront Distribution ID**: E28WEZM7X6L3BB
- **S3 Bucket**: auth-clear-frontend-prod

### API Gateway
- **Base URL**: https://umrmug9g7i.execute-api.us-east-1.amazonaws.com/prod
- **Region**: us-east-1

### API Endpoints

#### Merchant Endpoints
- `POST /merchants/onboard` - Onboard a new merchant
- `POST /merchants/notes` - Create merchant notes
- `POST /merchants/note-documents` - Upload merchant documents
- `POST /merchants/plaid/link-token` - Generate Plaid link token
- `POST /merchants/plaid/process-account` - Process Plaid account

#### Payment Endpoints
- `POST /payments/generate-integration-token` - Generate payment token
- `POST /payments/cleanup-integration-token` - Clean up expired tokens
- `POST /payments/validate-iframe-token` - Validate iframe token
- `POST /payments/process-token-payment` - Process payment
- `GET /payments/token-status` - Check token status
- `POST /payments/token-status` - Update token status

## 📊 AWS Resources

### DynamoDB Tables
- `PaymentTokens-prod` - Stores payment tokens with TTL
- `MerchantData-prod` - Stores merchant information

### CloudFormation Stacks
- `auth-clear-infra-prod` - VPC and networking
- `auth-clear-frontend-prod` - CloudFront and S3
- `auth-clear-functions-prod` - Lambda functions and API Gateway

## 🚀 Deployment Commands

### Deploy Everything
```bash
npm run deploy:prod:all
```

### Deploy Individual Stacks
```bash
npm run deploy:prod:infra     # Infrastructure
npm run deploy:prod:frontend  # Frontend
npm run deploy:prod:functions # Backend functions
```

### Remove Production (USE WITH CAUTION!)
```bash
npm run remove:prod:all
```

## 🔑 Environment Variables

### Frontend (.env.production)
```
VITE_API_URL=https://umrmug9g7i.execute-api.us-east-1.amazonaws.com/prod
VITE_PAYRIX_PUBLIC_KEY=your-prod-payrix-public-key
VITE_PAYRIX_IFRAME_URL=https://test-api.payrix.com/payFieldsScript
```

### Backend (AWS Systems Manager)
- Set production Payrix API keys in SSM Parameter Store
- Use `npm run generate-env:prod` to generate local env

## ⚠️ Important Notes

1. **CloudFront Propagation**: Changes to CloudFront can take 15-30 minutes to propagate globally
2. **Payrix Keys**: Currently using test keys - update before going live
3. **CORS**: API is configured to accept requests from CloudFront domain
4. **Custom Domain**: Ready for app.auth-clear.com setup in Hostinger

## 📝 Next Steps for Custom Domain

1. Request SSL certificate for app.auth-clear.com in ACM
2. Update CloudFront with certificate and alias
3. Add CNAME record in Hostinger:
   - Name: `app`
   - Value: `d2bpf0tsg3n3wi.cloudfront.net`

## 🔍 Testing

### Test Frontend
```bash
curl -I https://d2bpf0tsg3n3wi.cloudfront.net
```

### Test API
```bash
curl "https://umrmug9g7i.execute-api.us-east-1.amazonaws.com/prod/payments/token-status?token=test"
```

## 📅 Deployment Date
- **Deployed**: $(date)
- **AWS Profile**: payrix
- **Stage**: prod